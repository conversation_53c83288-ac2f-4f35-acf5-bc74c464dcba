// ==UserScript==
// @name         CISP自动答题助手
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  CISP考试系统自动答题、记录学习进度并导出答案脚本
// <AUTHOR>
// @match        *://*.cisp.org.cn/*
// @match        *://*.exam.cisp.org.cn/*
// @grant        GM_addStyle
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_deleteValue
// @grant        unsafeWindow
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';
    
    // 配置参数
    const CONFIG = {
        debugMode: false,               // 调试模式开关
        autoSpeed: 1,                   // 自动答题速度倍率（1-5）
        maxRetries: 3,                  // 最大重试次数
        waitInterval: 1000,             // 基础等待时间(ms)
        questionBtnSelector: '.answerSheetCls li, .exam-item, .question-btn', // 题目按钮选择器
        questionContentSelector: '.subject-text, .question-content, .q-body', // 题目内容选择器
        optionsSelector: '.options .option, .answer-options li, .options-item', // 选项选择器
        answerSelector: '.answer-text, .correct-answer, .q-answer', // 正确答案选择器
        analysisSelector: '.analysis-text, .answer-analysis, .q-analysis' // 解析选择器
    };
    
    // 全局变量
    let isRunning = false;              // 脚本运行状态
    let currentQuestionIndex = 0;       // 当前题目索引
    let answeredCount = 0;              // 已答题数量
    let totalQuestions = 0;             // 总题目数量
    let learningRecords = [];           // 学习记录数组
    let uiPanel = null;                 // UI面板元素
    let statusText = null;              // 状态文本元素
    
    /**
     * 初始化函数 - 脚本入口点
     */
    function initialize() {
        console.log('CISP自动答题助手初始化中...');
        createUIPanel();
        registerEventListeners();
        
        // 从存储中恢复学习记录
        const savedRecords = GM_getValue('learningRecords');
        if (savedRecords) {
            learningRecords = JSON.parse(savedRecords);
            updateStatusText(`已从存储中恢复${learningRecords.length}条学习记录`);
        }
    }
    
    /**
     * 创建UI控制面板
     */
    function createUIPanel() {
        // 添加样式
        GM_addStyle(`
            #cisp-helper-panel {
                position: fixed;
                top: 10px;
                right: 10px;
                width: 250px;
                background: rgba(255, 255, 255, 0.95);
                border: 1px solid #ddd;
                border-radius: 5px;
                box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
                z-index: 9999;
                font-family: Arial, sans-serif;
                padding: 10px;
                transition: all 0.3s ease;
            }
            #cisp-helper-panel:hover {
                box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
            }
            #cisp-helper-panel .panel-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;
                padding-bottom: 5px;
                border-bottom: 1px solid #eee;
            }
            #cisp-helper-panel .panel-title {
                font-weight: bold;
                font-size: 14px;
                color: #333;
            }
            #cisp-helper-panel .panel-content {
                margin-bottom: 10px;
            }
            #cisp-helper-panel .status-text {
                font-size: 12px;
                color: #666;
                margin-bottom: 10px;
                height: 40px;
                overflow-y: auto;
            }
            #cisp-helper-panel .control-btn {
                background: #4CAF50;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 3px;
                cursor: pointer;
                margin-right: 5px;
                font-size: 12px;
                transition: background 0.3s;
            }
            #cisp-helper-panel .control-btn:hover {
                background: #3e8e41;
            }
            #cisp-helper-panel .control-btn.stop {
                background: #f44336;
            }
            #cisp-helper-panel .control-btn.stop:hover {
                background: #d32f2f;
            }
            #cisp-helper-panel .control-btn.export {
                background: #2196F3;
            }
            #cisp-helper-panel .control-btn.export:hover {
                background: #0b7dda;
            }
            #cisp-helper-panel .control-row {
                display: flex;
                justify-content: space-between;
                margin-bottom: 8px;
            }
            #cisp-helper-panel .speed-control {
                display: flex;
                align-items: center;
                font-size: 12px;
            }
            #cisp-helper-panel .speed-label {
                margin-right: 5px;
            }
            #cisp-helper-panel .speed-value {
                width: 30px;
                text-align: center;
            }
            #cisp-helper-panel .checkbox-control {
                display: flex;
                align-items: center;
                font-size: 12px;
            }
            #cisp-helper-panel .drag-handle {
                cursor: move;
                padding: 0 5px;
            }
            #cisp-helper-panel .progress-bar {
                height: 5px;
                background: #e0e0e0;
                border-radius: 2px;
                margin-top: 5px;
            }
            #cisp-helper-panel .progress-value {
                height: 100%;
                background: #4CAF50;
                border-radius: 2px;
                width: 0%;
                transition: width 0.3s;
            }
        `);
        
        // 创建面板元素
        uiPanel = document.createElement('div');
        uiPanel.id = 'cisp-helper-panel';
        uiPanel.innerHTML = `
            <div class="panel-header">
                <div class="panel-title">CISP自动答题助手</div>
                <div class="drag-handle">≡</div>
            </div>
            <div class="panel-content">
                <div class="status-text">等待开始...</div>
                <div class="progress-bar">
                    <div class="progress-value"></div>
                </div>
                <div class="control-row">
                    <button id="start-btn" class="control-btn">开始答题</button>
                    <button id="stop-btn" class="control-btn stop" style="display:none">停止</button>
                    <button id="export-btn" class="control-btn export">导出记录</button>
                </div>
                <div class="control-row">
                    <div class="speed-control">
                        <span class="speed-label">速度:</span>
                        <button id="speed-down" class="control-btn">-</button>
                        <span id="speed-value" class="speed-value">1</span>
                        <button id="speed-up" class="control-btn">+</button>
                    </div>
                    <div class="checkbox-control">
                        <input type="checkbox" id="debug-mode">
                        <label for="debug-mode">调试模式</label>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(uiPanel);
        statusText = uiPanel.querySelector('.status-text');
        
        // 使面板可拖动
        makeDraggable(uiPanel, uiPanel.querySelector('.drag-handle'));
    }
    
    /**
     * 使元素可拖动
     * @param {HTMLElement} element - 要拖动的元素
     * @param {HTMLElement} handle - 拖动的手柄元素
     */
    function makeDraggable(element, handle) {
        let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
        
        handle.onmousedown = dragMouseDown;
        
        function dragMouseDown(e) {
            e.preventDefault();
            pos3 = e.clientX;
            pos4 = e.clientY;
            document.onmouseup = closeDragElement;
            document.onmousemove = elementDrag;
        }
        
        function elementDrag(e) {
            e.preventDefault();
            pos1 = pos3 - e.clientX;
            pos2 = pos4 - e.clientY;
            pos3 = e.clientX;
            pos4 = e.clientY;
            element.style.top = (element.offsetTop - pos2) + "px";
            element.style.left = (element.offsetLeft - pos1) + "px";
            element.style.right = "auto";
        }
        
        function closeDragElement() {
            document.onmouseup = null;
            document.onmousemove = null;
        }
    }
    
    /**
     * 注册UI事件监听器
     */
    function registerEventListeners() {
        const startBtn = document.getElementById('start-btn');
        const stopBtn = document.getElementById('stop-btn');
        const exportBtn = document.getElementById('export-btn');
        const speedUpBtn = document.getElementById('speed-up');
        const speedDownBtn = document.getElementById('speed-down');
        const speedValueEl = document.getElementById('speed-value');
        const debugModeCheckbox = document.getElementById('debug-mode');
        
        startBtn.addEventListener('click', () => {
            if (!isRunning) {
                isRunning = true;
                startBtn.style.display = 'none';
                stopBtn.style.display = 'inline-block';
                updateStatusText('自动答题已开始...');
                startAutoAnswering();
            }
        });
        
        stopBtn.addEventListener('click', () => {
            isRunning = false;
            stopBtn.style.display = 'none';
            startBtn.style.display = 'inline-block';
            updateStatusText('自动答题已停止');
        });
        
        exportBtn.addEventListener('click', exportLearningRecords);
        
        speedUpBtn.addEventListener('click', () => {
            if (CONFIG.autoSpeed < 5) {
                CONFIG.autoSpeed++;
                speedValueEl.textContent = CONFIG.autoSpeed;
            }
        });
        
        speedDownBtn.addEventListener('click', () => {
            if (CONFIG.autoSpeed > 1) {
                CONFIG.autoSpeed--;
                speedValueEl.textContent = CONFIG.autoSpeed;
            }
        });
        
        debugModeCheckbox.addEventListener('change', (e) => {
            CONFIG.debugMode = e.target.checked;
            updateStatusText(`调试模式: ${CONFIG.debugMode ? '开启' : '关闭'}`);
        });
    }
    
    /**
     * 更新状态文本
     * @param {string} text - 状态信息
     */
    function updateStatusText(text) {
        if (statusText) {
            const timestamp = new Date().toLocaleTimeString();
            statusText.innerHTML = `[${timestamp}] ${text}<br>` + statusText.innerHTML;
        }
        debug(text);
    }
    
    /**
     * 调试输出
     * @param {string} text - 调试信息
     */
    function debug(text) {
        if (CONFIG.debugMode) {
            console.log(`[CISP助手] ${text}`);
        }
    }
    
    /**
     * 更新进度条
     */
    function updateProgressBar() {
        if (totalQuestions > 0) {
            const progressPercent = (answeredCount / totalQuestions) * 100;
            const progressBar = document.querySelector('#cisp-helper-panel .progress-value');
            if (progressBar) {
                progressBar.style.width = `${progressPercent}%`;
            }
        }
    }
    
    /**
     * 开始自动答题流程
     */
    async function startAutoAnswering() {
        debug('开始自动答题流程');
        
        // 获取所有题目按钮
        const questionButtons = await getQuestionButtons();
        if (!questionButtons || questionButtons.length === 0) {
            updateStatusText('未找到题目按钮，请检查页面结构');
            isRunning = false;
            document.getElementById('stop-btn').style.display = 'none';
            document.getElementById('start-btn').style.display = 'inline-block';
            return;
        }
        
        totalQuestions = questionButtons.length;
        updateStatusText(`共发现${totalQuestions}道题目`);
        
        // 循环处理每道题
        currentQuestionIndex = 0;
        answeredCount = 0;
        
        while (isRunning && currentQuestionIndex < totalQuestions) {
            const currentButton = questionButtons[currentQuestionIndex];
            
            // 检查题目是否已答
            const isAnswered = checkIfAnswered(currentButton);
            
            if (!isAnswered) {
                // 点击题目按钮
                debug(`点击第 ${currentQuestionIndex + 1} 道题目按钮`);
                simulateMouseClick(currentButton);
                
                // 等待题目内容加载
                await sleep(getWaitTime(1.5));
                
                // 处理题目
                const success = await processCurrentQuestion();
                if (success) {
                    answeredCount++;
                    updateProgressBar();
                }
            } else {
                debug(`第 ${currentQuestionIndex + 1} 道题目已答，跳过`);
                answeredCount++;
                updateProgressBar();
            }
            
            currentQuestionIndex++;
            
            // 随机等待时间，避免被检测
            await sleep(getWaitTime(1 + Math.random()));
        }
        
        if (isRunning) {
            updateStatusText(`自动答题完成，共完成 ${answeredCount}/${totalQuestions} 道题目`);
            isRunning = false;
            document.getElementById('stop-btn').style.display = 'none';
            document.getElementById('start-btn').style.display = 'inline-block';
        }
    }
    
    /**
     * 获取所有题目按钮
     * @returns {Promise<Array<HTMLElement>>} 题目按钮数组
     */
    async function getQuestionButtons() {
        debug('查找题目按钮');
        
        // 尝试多种选择器查找题目按钮
        let buttons = [];
        const selectors = CONFIG.questionBtnSelector.split(',');
        
        for (const selector of selectors) {
            const foundElements = document.querySelectorAll(selector.trim());
            if (foundElements && foundElements.length > 0) {
                buttons = Array.from(foundElements);
                debug(`使用选择器 ${selector.trim()} 找到 ${buttons.length} 个题目按钮`);
                break;
            }
        }
        
        if (buttons.length === 0) {
            // 尝试基于DOM结构特征查找
            const possibleContainers = document.querySelectorAll('.answer-sheet, .question-list, .exam-items');
            for (const container of possibleContainers) {
                const childButtons = Array.from(container.children).filter(el => {
                    return el.tagName === 'LI' || el.tagName === 'DIV' || 
                           el.classList.contains('item') || el.classList.contains('btn');
                });
                
                if (childButtons.length > 0) {
                    buttons = childButtons;
                    debug(`通过容器找到 ${buttons.length} 个题目按钮`);
                    break;
                }
            }
        }
        
        // 过滤无效按钮（通常题目按钮含有数字）
        return buttons.filter(btn => {
            const text = btn.textContent.trim();
            return /\d+/.test(text) || btn.hasAttribute('data-index') || btn.hasAttribute('data-id');
        });
    }
    
    /**
     * 检查题目是否已答
     * @param {HTMLElement} button - 题目按钮元素
     * @returns {boolean} 是否已答题
     */
    function checkIfAnswered(button) {
        // 根据按钮样式判断是否已答题
        // 通常已答题的按钮会有特殊的类名或样式
        
        // 方法1: 通过类名判断
        if (button.classList.contains('answered') || 
            button.classList.contains('complete') || 
            button.classList.contains('done')) {
            return true;
        }
        
        // 方法2: 通过背景色判断
        const bgColor = window.getComputedStyle(button).backgroundColor;
        
        // 已答题按钮通常为绿色或蓝色背景
        if (bgColor.includes('rgb(0, 128') || 
            bgColor.includes('rgb(33, 150') || 
            bgColor.includes('rgb(0, 0, 255') || 
            bgColor.includes('rgb(0, 100')) {
            return true;
        }
        
        // 方法3: 通过边框色判断
        const borderColor = window.getComputedStyle(button).borderColor;
        if (borderColor.includes('rgb(0, 128') || 
            borderColor.includes('rgb(33, 150') || 
            borderColor.includes('rgb(0, 0, 255')) {
            return true;
        }
        
        // 方法4: 通过数据属性判断
        if (button.getAttribute('data-status') === 'answered' || 
            button.getAttribute('data-answered') === 'true') {
            return true;
        }
        
        return false;
    }
    
    /**
     * 处理当前题目
     * @returns {Promise<boolean>} 是否成功处理
     */
    async function processCurrentQuestion() {
        debug('处理当前题目');
        
        try {
            // 获取题目信息
            const questionInfo = await getQuestionInfo();
            if (!questionInfo) {
                updateStatusText('无法获取题目信息，跳过当前题目');
                return false;
            }
            
            // 随机选择一个选项
            const optionElements = await getOptionElements();
            if (!optionElements || optionElements.length === 0) {
                updateStatusText('未找到选项元素，跳过当前题目');
                return false;
            }
            
            // 随机选择一个选项
            const randomIndex = Math.floor(Math.random() * optionElements.length);
            const selectedOption = optionElements[randomIndex];
            
            // 选择该选项
            const selectSuccess = await selectOption(selectedOption);
            if (!selectSuccess) {
                updateStatusText('选择选项失败，跳过当前题目');
                return false;
            }
            
            // 等待选择生效
            await sleep(getWaitTime(1));
            
            // 获取正确答案和解析
            const answerInfo = await getAnswerInfo();
            
            // 记录学习记录
            recordLearningData(questionInfo, selectedOption, answerInfo);
            
            return true;
        } catch (error) {
            debug(`处理题目出错: ${error.message}`);
            return false;
        }
    }
    
    /**
     * 获取题目信息
     * @returns {Promise<object|null>} 题目信息对象
     */
    async function getQuestionInfo() {
        debug('获取题目信息');
        
        for (let retry = 0; retry < CONFIG.maxRetries; retry++) {
            try {
                // 获取题目内容
                let questionContent = '';
                const contentSelectors = CONFIG.questionContentSelector.split(',');
                
                for (const selector of contentSelectors) {
                    const element = document.querySelector(selector.trim());
                    if (element) {
                        questionContent = element.textContent.trim();
                        break;
                    }
                }
                
                if (!questionContent) {
                    // 尝试查找具有特定特征的元素
                    const possibleElements = document.querySelectorAll('div.question, div.subject, div.q-item');
                    for (const el of possibleElements) {
                        if (el.textContent.includes('题') || el.textContent.length > 20) {
                            questionContent = el.textContent.trim();
                            break;
                        }
                    }
                }
                
                if (questionContent) {
                    return {
                        index: currentQuestionIndex + 1,
                        content: questionContent
                    };
                }
                
                // 如果未找到，等待后重试
                await sleep(getWaitTime(1));
            } catch (error) {
                debug(`获取题目信息出错: ${error.message}`);
                await sleep(getWaitTime(0.5));
            }
        }
        
        return null;
    }
    
    /**
     * 获取选项元素
     * @returns {Promise<Array<HTMLElement>|null>} 选项元素数组
     */
    async function getOptionElements() {
        debug('获取选项元素');
        
        for (let retry = 0; retry < CONFIG.maxRetries; retry++) {
            try {
                let options = [];
                const optionsSelectors = CONFIG.optionsSelector.split(',');
                
                for (const selector of optionsSelectors) {
                    const elements = document.querySelectorAll(selector.trim());
                    if (elements && elements.length > 0) {
                        options = Array.from(elements);
                        break;
                    }
                }
                
                if (options.length === 0) {
                    // 尝试查找具有特定特征的元素
                    const inputs = document.querySelectorAll('input[type="radio"], input[type="checkbox"]');
                    if (inputs.length > 0) {
                        // 获取输入元素的父元素作为选项元素
                        options = Array.from(inputs).map(input => input.parentElement);
                    } else {
                        // 尝试查找下拉选择元素
                        const selects = document.querySelectorAll('select');
                        if (selects.length > 0) {
                            options = Array.from(selects[0].options);
                        }
                    }
                }
                
                if (options.length > 0) {
                    return options;
                }
                
                // 如果未找到，等待后重试
                await sleep(getWaitTime(1));
            } catch (error) {
                debug(`获取选项元素出错: ${error.message}`);
                await sleep(getWaitTime(0.5));
            }
        }
        
        return null;
    }
    
    /**
     * 选择选项
     * @param {HTMLElement} optionElement - 选项元素
     * @returns {Promise<boolean>} 是否成功选择
     */
    async function selectOption(optionElement) {
        debug('选择选项: ' + optionElement.textContent.trim());
        
        try {
            // 判断选项类型
            let isRadio = false;
            let isCheckbox = false;
            let isSelect = false;
            
            // 查找选项内的输入元素
            const radioInput = optionElement.querySelector('input[type="radio"]');
            const checkboxInput = optionElement.querySelector('input[type="checkbox"]');
            
            if (radioInput) {
                isRadio = true;
            } else if (checkboxInput) {
                isCheckbox = true;
            } else if (optionElement.tagName === 'OPTION') {
                isSelect = true;
            }
            
            // 根据选项类型执行不同的选择操作
            if (isRadio || isCheckbox) {
                const inputElement = radioInput || checkboxInput;
                
                // 模拟真实的鼠标事件序列
                simulateMouseInteraction(optionElement);
                
                // 验证是否选中
                return await verifyOptionSelected(inputElement);
            } else if (isSelect) {
                // 如果是下拉选择框的选项
                const selectElement = optionElement.parentElement;
                selectElement.value = optionElement.value;
                
                // 触发change事件
                simulateEvent(selectElement, 'change');
                
                // 验证是否选中
                return optionElement.selected;
            } else {
                // 直接点击选项元素
                simulateMouseInteraction(optionElement);
                
                // 由于没有明确的选中状态指标，等待一段时间后假定成功
                await sleep(getWaitTime(1));
                return true;
            }
        } catch (error) {
            debug(`选择选项出错: ${error.message}`);
            return false;
        }
    }
    
    /**
     * 模拟鼠标交互
     * @param {HTMLElement} element - 目标元素
     */
    function simulateMouseInteraction(element) {
        // 获取元素的位置
        const rect = element.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        
        // 模拟鼠标悬停
        simulateMouseEvent(element, 'mouseover', centerX, centerY);
        
        // 模拟鼠标移动
        simulateMouseEvent(element, 'mousemove', centerX, centerY);
        
        // 模拟鼠标按下
        simulateMouseEvent(element, 'mousedown', centerX, centerY);
        
        // 模拟鼠标释放
        simulateMouseEvent(element, 'mouseup', centerX, centerY);
        
        // 模拟鼠标点击
        simulateMouseEvent(element, 'click', centerX, centerY);
        
        // 如果元素是输入元素，触发额外事件
        if (element.tagName === 'INPUT' || element.querySelector('input')) {
            const inputEl = element.tagName === 'INPUT' ? element : element.querySelector('input');
            if (inputEl) {
                simulateEvent(inputEl, 'change');
                simulateEvent(inputEl, 'input');
            }
        }
    }
    
    /**
     * 模拟鼠标事件
     * @param {HTMLElement} element - 目标元素
     * @param {string} eventType - 事件类型
     * @param {number} x - 鼠标X坐标
     * @param {number} y - 鼠标Y坐标
     */
    function simulateMouseEvent(element, eventType, x, y) {
        const event = new MouseEvent(eventType, {
            view: window,
            bubbles: true,
            cancelable: true,
            clientX: x,
            clientY: y
        });
        element.dispatchEvent(event);
    }
    
    /**
     * 模拟普通事件
     * @param {HTMLElement} element - 目标元素
     * @param {string} eventType - 事件类型
     */
    function simulateEvent(element, eventType) {
        const event = new Event(eventType, {
            bubbles: true,
            cancelable: true
        });
        element.dispatchEvent(event);
    }
    
    /**
     * 模拟鼠标点击
     * @param {HTMLElement} element - 要点击的元素
     */
    function simulateMouseClick(element) {
        const rect = element.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;
        
        simulateMouseEvent(element, 'mouseover', centerX, centerY);
        simulateMouseEvent(element, 'mousedown', centerX, centerY);
        simulateMouseEvent(element, 'mouseup', centerX, centerY);
        simulateMouseEvent(element, 'click', centerX, centerY);
    }
    
    /**
     * 验证选项是否被选中
     * @param {HTMLElement} inputElement - 输入元素
     * @returns {Promise<boolean>} 是否选中
     */
    async function verifyOptionSelected(inputElement) {
        // 初始尝试次数
        let attempts = 0;
        const maxAttempts = 3;
        
        while (attempts < maxAttempts) {
            // 检查是否选中
            if (inputElement.checked) {
                return true;
            }
            
            // 检查ARIA属性
            if (inputElement.getAttribute('aria-checked') === 'true') {
                return true;
            }
            
            // 检查父元素是否有选中样式
            const parent = inputElement.parentElement;
            if (parent) {
                const classes = parent.classList;
                if (classes.contains('selected') || 
                    classes.contains('active') || 
                    classes.contains('checked')) {
                    return true;
                }
                
                // 检查背景色变化
                const bgColor = window.getComputedStyle(parent).backgroundColor;
                if (bgColor.includes('rgb(0,') || bgColor.includes('rgb(33,') || bgColor.includes('rgb(0, 0,')) {
                    return true;
                }
            }

            // 如果未检测到选中，再次尝试点击
            if (attempts < maxAttempts - 1) {
                simulateMouseClick(inputElement);
                await sleep(getWaitTime(0.5));
            }

            attempts++;
        }

        return false;
    }

/**
 * 获取答案信息
 * @returns {Promise<object>} 答案信息对象
 */
async function getAnswerInfo() {
    debug('获取答案信息');
    
    let correctAnswer = '';
    let analysis = '';
    
    // 尝试获取正确答案
    const answerSelectors = CONFIG.answerSelector.split(',');
    for (const selector of answerSelectors) {
        const element = document.querySelector(selector.trim());
        if (element) {
            correctAnswer = element.textContent.trim();
            break;
        }
    }
    
    // 如果未找到，尝试从页面文本中提取
    if (!correctAnswer) {
        const pageText = document.body.textContent;
        const answerMatch = pageText.match(/正确答案[：:]\s*([A-D]+)/i);
        if (answerMatch) {
            correctAnswer = answerMatch[1].trim();
        }
    }
    
    // 尝试获取解析
    const analysisSelectors = CONFIG.analysisSelector.split(',');
    for (const selector of analysisSelectors) {
        const element = document.querySelector(selector.trim());
        if (element) {
            analysis = element.textContent.trim();
            break;
        }
    }
    
    // 如果未找到，尝试从页面文本中提取
    if (!analysis) {
        const pageText = document.body.textContent;
        const analysisMatch = pageText.match(/解析[：:]\s*([\s\S]+?)(?=\n\n|\d+[\.、]|$)/i);
        if (analysisMatch) {
            analysis = analysisMatch[1].trim();
        }
    }
    
    return {
        correctAnswer,
        analysis
    };
}

/**
 * 记录学习数据
 * @param {object} questionInfo - 题目信息
 * @param {HTMLElement} selectedOption - 选择的选项元素
 * @param {object} answerInfo - 答案信息
 */
function recordLearningData(questionInfo, selectedOption, answerInfo) {
    const timestamp = new Date().toLocaleString();
    const selectedText = selectedOption.textContent.trim();
    
    // 从选择的选项中提取选项标识（A、B、C、D等）
    let selectedOptionId = '';
    const optionMatch = selectedText.match(/^([A-D])[\.、]/i);
    if (optionMatch) {
        selectedOptionId = optionMatch[1];
    } else {
        // 如果无法从文本提取，尝试获取索引位置
        const allOptions = Array.from(document.querySelectorAll(CONFIG.optionsSelector));
        const index = allOptions.indexOf(selectedOption);
        if (index !== -1) {
            selectedOptionId = String.fromCharCode(65 + index); // 65是A的ASCII码
        }
    }
    
    // 创建学习记录对象
    const record = {
        timestamp,
        questionNumber: questionInfo.index,
        questionContent: questionInfo.content,
        userAnswer: selectedOptionId || selectedText,
        correctAnswer: answerInfo.correctAnswer,
        analysis: answerInfo.analysis
    };
    
    // 添加到学习记录数组
    learningRecords.push(record);
    
    // 保存到存储
    GM_setValue('learningRecords', JSON.stringify(learningRecords));
    
    // 更新状态
    updateStatusText(`已记录第 ${questionInfo.index} 题的答题数据`);
}

/**
 * 导出学习记录
 */
function exportLearningRecords() {
    if (learningRecords.length === 0) {
        updateStatusText('没有学习记录可导出');
        return;
    }
    
    // 格式化记录为文本
    let content = '===== CISP学习记录 =====\n';
    content += `导出时间: ${new Date().toLocaleString()}\n`;
    content += `总题数: ${learningRecords.length}\n\n`;
    
    learningRecords.forEach((record) => {
        content += `题目 ${record.questionNumber}. ${record.questionContent}\n`;
        content += `我的答案: ${record.userAnswer}\n`;
        content += `正确答案: ${record.correctAnswer}\n`;
        
        if (record.analysis) {
            content += `解析: ${record.analysis}\n`;
        }
        
        content += `记录时间: ${record.timestamp}\n`;
        content += '----------------------------\n\n';
    });
    
    // 创建下载链接
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    a.href = url;
    a.download = `CISP学习记录_${timestamp}.txt`;
    document.body.appendChild(a);
    a.click();
    
    // 清理
    setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }, 100);
    
    updateStatusText(`已导出 ${learningRecords.length} 条学习记录`);
}

/**
 * 计算等待时间（考虑速度倍率）
 * @param {number} baseTime - 基础等待时间（秒）
 * @returns {number} 实际等待时间（毫秒）
 */
function getWaitTime(baseTime) {
    return (CONFIG.waitInterval * baseTime) / CONFIG.autoSpeed;
}

/**
 * 等待指定时间
 * @param {number} ms - 等待时间（毫秒）
 * @returns {Promise<void>}
 */
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// 页面加载完成后初始化脚本
if (document.readyState === 'complete') {
    initialize();
} else {
    window.addEventListener('load', initialize);
}})();